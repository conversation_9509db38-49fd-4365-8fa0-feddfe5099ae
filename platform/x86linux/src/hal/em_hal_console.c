#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <time.h>
#include <stdarg.h>
#include "em_hal_console.h"

static void get_time_fmt(char* str, int len)
{

}

static void pts_console_print_inner(const char* str)
{

}

static void pts_console_print_inner_time(const char* str)
{

}

void pts_console_print_fmt(int critical_flag, int time_flag, const char *fun, char *fmt, ...)
{

}

void em_hal_console_print(const char* str)
{
	
}
