#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <time.h>
#include <stdarg.h>
#include "em_hal_system.h"

void em_hal_screen_resume()
{

}

void em_hal_screen_suspend()
{

}

void em_hal_get_cpu_temp(char* buf, int len){

}

void em_hal_get_cpu_clk(char* buf, int len){

}

void em_hal_get_free_storage(char* buf, int len){

}

void em_hal_reboot(void){

}
